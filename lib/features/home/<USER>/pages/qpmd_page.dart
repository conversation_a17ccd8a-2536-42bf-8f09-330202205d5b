import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/date_picker_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/text_field_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/counter_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/dropdown_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/checkbox_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/multi_select_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/radio_button_widget.dart';

import '../../../auth/presentation/widgets/app_button.dart';

@RoutePage()
class QPMDPage extends StatefulWidget {
  final Question? question;
  final QuestionPart? questionPart;
  final num? taskId;
  final num? formId;

  const QPMDPage({
    super.key,
    this.question,
    this.questionPart,
    this.taskId,
    this.formId,
  });

  @override
  State<QPMDPage> createState() => _QPMDPageState();
}

class _QPMDPageState extends State<QPMDPage> {
  // State management for different measurement types
  final Map<num, dynamic> _measurementValues = {};

  // State management for validation errors
  final Map<num, String?> _validationErrors = {};

  // State management for widget visibility based on conditions
  final Map<num, bool> _widgetVisibility = {};

  @override
  void initState() {
    super.initState();
    _initializeMeasurementValues();
  }

  void _initializeMeasurementValues() {
    if (widget.question?.measurements != null) {
      // First, analyze dependencies to determine initial visibility
      final dependentWidgets = _analyzeDependencies();

      for (final measurement in widget.question!.measurements!) {
        if (measurement.measurementId != null) {
          final measurementId = measurement.measurementId!;

          // Set initial widget visibility based on dependency analysis
          // Widgets with no dependencies are visible by default
          // Widgets that are targets of conditional actions are hidden by default
          _widgetVisibility[measurementId] =
              !dependentWidgets.contains(measurementId);

          // Initialize with default values based on measurement type
          switch (measurement.measurementTypeId) {
            case 9: // Date picker
              _measurementValues[measurementId] = null;
              break;
            case 1: // Text field
            case 2: // Text field
              _measurementValues[measurementId] = '';
              break;
            case 7: // Counter
              _measurementValues[measurementId] = 0;
              break;
            case 4: // Dropdown
            case 5: // Dropdown
              _measurementValues[measurementId] = null;
              break;
            case 3: // Checkbox
              _measurementValues[measurementId] = false;
              break;
            case 6: // Multi-select
              _measurementValues[measurementId] = <String>[];
              break;
            default:
              _measurementValues[measurementId] = null;
          }
        }
      }
    }
  }

  /// Analyze measurement dependencies to determine initial widget visibility
  ///
  /// This method examines all measurement_conditions and measurement_conditions_multiple
  /// arrays to identify which widgets are targets of conditional actions.
  ///
  /// Returns a Set of measurement IDs that are dependent on other widgets
  /// (i.e., widgets that should be hidden by default and only shown when conditions are met)
  Set<num> _analyzeDependencies() {
    final dependentWidgets = <num>{};

    if (widget.question?.measurements == null) return dependentWidgets;

    // Analyze each measurement's conditions
    for (final measurement in widget.question!.measurements!) {
      if (measurement.measurementId == null) continue;

      // Check measurement_conditions array (used when is_mll is false)
      if (measurement.measurementConditions != null) {
        for (final condition in measurement.measurementConditions!) {
          if (condition.actionMeasurementId != null) {
            // This measurement ID is a target of a conditional action
            dependentWidgets.add(condition.actionMeasurementId!);
          }
        }
      }

      // Check measurement_conditions_multiple array (used when is_mll is true)
      if (measurement.measurementConditionsMultiple != null) {
        for (final condition in measurement.measurementConditionsMultiple!) {
          if (condition.actionMeasurementId != null) {
            // This measurement ID is a target of a conditional action
            dependentWidgets.add(condition.actionMeasurementId!);
          }
        }
      }
    }

    return dependentWidgets;
  }

  void _updateMeasurementValue(num measurementId, dynamic value) {
    setState(() {
      _measurementValues[measurementId] = value;
      // Clear validation error when value changes
      _validationErrors[measurementId] = null;

      // Check for conditional logic when dropdown values change
      final measurement = _findMeasurementById(measurementId);
      if (measurement != null &&
          [4, 5].contains(measurement.measurementTypeId)) {
        _processConditionalLogic(measurement, value);
      }
    });
  }

  /// Find a measurement by its ID
  Measurement? _findMeasurementById(num measurementId) {
    final measurements = widget.question?.measurements ?? [];
    try {
      return measurements.firstWhere(
        (measurement) => measurement.measurementId == measurementId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Process conditional logic for dropdown selections
  ///
  /// This method implements the conditional dropdown logic based on the question's is_mll property:
  /// - If is_mll is false: Uses measurement_conditions array
  /// - If is_mll is true: Uses measurement_conditions_multiple array
  ///
  /// For each condition, it checks if:
  /// - measurementId equals the current measurement ID
  /// - measurementOptionId equals the selected option ID
  ///
  /// If both conditions are satisfied, it applies the action (appear/disappear)
  /// to the target widget specified by action_measurement_id
  void _processConditionalLogic(
      Measurement measurement, dynamic selectedValue) {
    if (widget.question?.isMll == null || selectedValue == null) return;

    // Get the selected option ID
    final selectedOptionId = _getSelectedOptionId(measurement, selectedValue);
    if (selectedOptionId == null) return;

    // Check conditions based on is_mll property
    if (widget.question!.isMll == false) {
      // Use measurement_conditions array
      _processConditions(
        measurement.measurementConditions,
        measurement.measurementId,
        selectedOptionId,
      );
    } else {
      // Use measurement_conditions_multiple array
      _processConditions(
        measurement.measurementConditionsMultiple,
        measurement.measurementId,
        selectedOptionId,
      );
    }
  }

  /// Get the measurement option ID for the selected value
  num? _getSelectedOptionId(Measurement measurement, dynamic selectedValue) {
    if (measurement.measurementOptions == null) return null;

    try {
      final selectedOption = measurement.measurementOptions!.firstWhere(
        (option) => option.measurementOptionDescription == selectedValue,
      );
      return selectedOption.measurementOptionId;
    } catch (e) {
      return null;
    }
  }

  /// Process measurement conditions array
  void _processConditions(
    List<MeasurementCondition>? conditions,
    num? currentMeasurementId,
    num selectedOptionId,
  ) {
    if (conditions == null || currentMeasurementId == null) return;

    for (final condition in conditions) {
      // Check if this condition applies to the current measurement and selected option
      if (condition.measurementId == currentMeasurementId &&
          condition.measurementOptionId == selectedOptionId) {
        // Apply the action to the target measurement
        if (condition.actionMeasurementId != null && condition.action != null) {
          _applyConditionalAction(
            condition.actionMeasurementId!,
            condition.action!,
          );
        }
      }
    }

    // Also check for conditions that should hide widgets when this option is NOT selected
    // This handles cases where selecting a different option should hide previously shown widgets
    _processReverseConditions(
        conditions, currentMeasurementId, selectedOptionId);
  }

  /// Apply conditional action (show/hide) to a target measurement widget
  void _applyConditionalAction(num targetMeasurementId, String action) {
    // Update widget visibility based on action
    if (action.toLowerCase() == 'appear') {
      _widgetVisibility[targetMeasurementId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      _widgetVisibility[targetMeasurementId] = false;
    }
  }

  /// Process reverse conditions to hide widgets when their trigger conditions are no longer met
  ///
  /// This method ensures that when a dropdown selection changes, widgets that were previously
  /// shown by other options are hidden if their conditions are no longer satisfied.
  void _processReverseConditions(
    List<MeasurementCondition>? conditions,
    num? currentMeasurementId,
    num selectedOptionId,
  ) {
    if (conditions == null || currentMeasurementId == null) return;

    // Find all conditions for the current measurement that are NOT the selected option
    for (final condition in conditions) {
      if (condition.measurementId == currentMeasurementId &&
          condition.measurementOptionId != selectedOptionId &&
          condition.actionMeasurementId != null &&
          condition.action != null) {
        // If this condition would show a widget, but it's not the selected option,
        // we should hide that widget (reverse the action)
        if (condition.action!.toLowerCase() == 'appear') {
          _widgetVisibility[condition.actionMeasurementId!] = false;
        }
        // Note: We don't reverse 'disappear' actions as they might be intentional
      }
    }
  }

  /// Validate a single measurement value
  String? _validateMeasurement(Measurement measurement, dynamic value) {
    if (measurement.measurementValidations == null) return null;

    for (final validation in measurement.measurementValidations!) {
      // Validation Type 1: Required validation
      if (validation.validationTypeId == 1 && validation.required == true) {
        if (_isValueEmpty(value, measurement.measurementTypeId)) {
          return validation.errorMessage ?? 'This field is required';
        }
      }

      // Validation Type 2: Range validation
      if (validation.validationTypeId == 2 &&
          validation.rangeValidation != null &&
          validation.rangeValidation!.isNotEmpty) {
        logger('Range validation: $value');
        value = int.tryParse(value.toString());
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          if (!_validateRange(value, validation.rangeValidation!)) {
            return validation.errorMessage ??
                'Value is not within the valid range';
          }
        }
      }

      // Validation Type 3: Expression validation (regex)
      if (validation.validationTypeId == 3 &&
          validation.expressionValidation != null &&
          validation.expressionValidation!.isNotEmpty) {
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          if (!_validateExpression(value, validation.expressionValidation!)) {
            return validation.errorMessage ??
                'Value does not match the required format';
          }
        }
      }
    }

    return null;
  }

  /// Check if a value is considered empty based on measurement type
  bool _isValueEmpty(dynamic value, num? measurementTypeId) {
    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return value == null || value.toString().trim().isEmpty;
      case 3: // Checkbox
        return value == null || value == false;
      case 4: // Dropdown
      case 5: // Dropdown
      case 9: // Date picker
        return value == null;
      case 6: // Multi-select
        return value == null || (value is List && value.isEmpty);
      case 7: // Counter
        return value == null || value == 0;
      default:
        return value == null;
    }
  }

  /// Validate range for numeric values
  bool _validateRange(dynamic value, String rangeValidation) {
    try {
      final ranges = rangeValidation.split('|');
      final numValue = double.tryParse(value.toString());
      if (numValue == null) return false;
      final min = double.tryParse(ranges[0].trim());
      final max = double.tryParse(ranges[1].trim());
      if (min != null && max != null) {
        if (numValue >= min && numValue <= max) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Validate expression using regex
  bool _validateExpression(dynamic value, String expressionValidation) {
    try {
      final regex = RegExp(expressionValidation);
      return regex.hasMatch(value.toString());
    } catch (e) {
      return false;
    }
  }

  /// Check if a measurement is required based on measurement_validations array
  bool _isMeasurementRequired(Measurement measurement) {
    if (measurement.measurementValidations == null) return false;

    for (final validation in measurement.measurementValidations!) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Generate QuestionAnswer array from current measurement values
  List<QuestionAnswer> _generateQuestionAnswers() {
    final questionAnswers = <QuestionAnswer>[];
    final measurements = widget.question?.measurements ?? [];

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Only include answers for visible widgets
      final isVisible = _widgetVisibility[measurementId] ?? true;
      if (!isVisible) continue;

      final value = _measurementValues[measurementId];

      // Create QuestionAnswer object
      final questionAnswer = QuestionAnswer(
        taskId: widget.taskId,
        flip: widget.question?.flip,
        formId: widget.formId,
        questionId: widget.question?.questionId,
        isComment: false,
        commentTypeId: null,
        questionpartId: widget.questionPart?.questionpartId,
        questionPartMultiId: widget.questionPart?.questionpartId?.toString(),
        measurementId: measurementId,
        measurementTypeId: measurement.measurementTypeId,
      );

      // Set values based on measurement type
      if ([1, 2, 9, 7].contains(measurement.measurementTypeId)) {
        // Case 1: Text Field, Date Picker, Counter
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;

        if (measurement.measurementTypeId == 3) {
          // Checkbox case
          questionAnswer.measurementTextResult = value == true ? '1' : '2';
        } else {
          // Text field, date picker, counter
          questionAnswer.measurementTextResult =
              _isValueEmpty(value, measurement.measurementTypeId)
                  ? null
                  : value.toString();
        }
      } else if ([4, 5].contains(measurement.measurementTypeId)) {
        // Case 2: Dropdown
        if (value != null && measurement.measurementOptions != null) {
          // Find the selected option
          final selectedOption = measurement.measurementOptions!.firstWhere(
              (option) => option.measurementOptionDescription == value,
              orElse: () => MeasurementOption());

          questionAnswer.measurementOptionId =
              selectedOption.measurementOptionId;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = value.toString();
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
      } else if (measurement.measurementTypeId == 3) {
        // Case 3: Checkbox
        questionAnswer.measurementOptionId = value == true ? 1 : 2;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = value == true ? '1' : '2';
      } else if (measurement.measurementTypeId == 6) {
        // Case 4: Multi-select
        if (value != null &&
            value is List<String> &&
            measurement.measurementOptions != null) {
          final optionIds = <num>[];

          for (final selectedValue in value) {
            final option = measurement.measurementOptions!.firstWhere(
                (opt) => opt.measurementOptionDescription == selectedValue,
                orElse: () => MeasurementOption());
            if (option.measurementOptionId != null) {
              optionIds.add(option.measurementOptionId!);
            }
          }

          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = optionIds.join(',');
          questionAnswer.measurementTextResult = value.join('|');
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
      }

      questionAnswers.add(questionAnswer);
    }

    return questionAnswers;
  }

  /// Validate all measurements and return true if all are valid
  bool _validateAllMeasurements() {
    bool isValid = true;
    final measurements = widget.question?.measurements ?? [];

    setState(() {
      _validationErrors.clear();
    });

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Only validate visible widgets
      final isVisible = _widgetVisibility[measurementId] ?? true;
      if (!isVisible) continue;

      final value = _measurementValues[measurementId];

      // Perform validation
      final error = _validateMeasurement(measurement, value);
      if (error != null) {
        setState(() {
          _validationErrors[measurementId] = error;
        });
        isValid = false;
      }

      // Special validation for dropdown with form_type_id = 12
      if ([4, 5].contains(measurement.measurementTypeId) &&
          widget.formId != null) {
        // Check if this is a quiz form (form_type_id = 12)
        // This would need to be passed from the parent or retrieved from context
        // For now, we'll skip this validation as we don't have access to forms array
      }
    }

    return isValid;
  }

  /// Handle save button press
  void _handleSave() {
    if (_validateAllMeasurements()) {
      final questionAnswers = _generateQuestionAnswers();

      // TODO: Save questionAnswers to database or send to API
      // This would typically involve calling a cubit/bloc method

      // For now, just show a success message and navigate back
      if (mounted) {
        SnackBarService.success(
          context: context,
          message: 'Answers saved successfully!',
        );
      }

      // Navigate back
      Navigator.of(context).pop();
    } else {
      // Show error message
      SnackBarService.error(
        context: context,
        message: 'Please fix the validation errors before saving.',
      );
    }
  }

  /// Get camera icon info for a measurement based on photo_tags_three array
  Map<String, dynamic> _getCameraIconInfo(Measurement measurement) {
    final result = {'show': false, 'isMandatory': false};

    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return result;
    }

    // Check photo_tags_three array for matching questionpart_id and measurement_id
    for (final photoTag in widget.question!.photoTagsThree!) {
      if (photoTag.measurementId == measurement.measurementId &&
          photoTag.questionpartId == widget.questionPart?.questionpartId) {
        result['show'] = true;
        result['isMandatory'] = photoTag.isMandatory == true;
        break;
      }
    }

    return result;
  }

  /// Handle navigation to MPTPage when "Add Photos" section is tapped
  void _handleAddPhotosTap(Measurement measurement) {
    logger('Add photos tapped for measurement: ${measurement.measurementId}');
    logger('Add photos tapped for measurement: ${measurement.measurementId}');
    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return;
    }

    // Navigate to MPTPage with level 3 for photo_tags_three
    context.router.push(MPTRoute(
      taskId: widget.taskId?.toString(),
      formId: widget.formId?.toString(),
      questionId: widget.question?.questionId?.toString(),
      questionPartId: widget.questionPart?.questionpartId?.toString(),
      measurementId: measurement.measurementId?.toString(),
      images: const [], // Start with empty images
      question: widget.question,
      level: 3, // Use level 3 for photo_tags_three array
    ));
  }

  Widget _buildMeasurementWidget(Measurement measurement) {
    final measurementId = measurement.measurementId;
    if (measurementId == null) return const SizedBox.shrink();

    // Check widget visibility based on conditional logic
    final isVisible = _widgetVisibility[measurementId] ?? true;
    if (!isVisible) return const SizedBox.shrink();

    final isRequired = _isMeasurementRequired(measurement);
    final cameraInfo = _getCameraIconInfo(measurement);

    Widget measurementWidget;
    switch (measurement.measurementTypeId) {
      case 9: // Date picker
        measurementWidget = DatePickerWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 1: // Text field
      case 2: // Text field
        measurementWidget = TextFieldWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? '',
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 7: // Counter
        measurementWidget = CounterWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? 0,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 4: // Dropdown
      case 5: // Dropdown
        measurementWidget = DropdownWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 3: // Checkbox
        measurementWidget = CheckboxWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? false,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 6: // Multi-select
        measurementWidget = MultiSelectWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? <String>[],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
        );
        break;
      case 8: // Radio button (single choice)
        measurementWidget = RadioButtonWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
        );
        break;
      default:
        measurementWidget = Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              'Unsupported measurement type: ${measurement.measurementTypeId}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.blackTint1,
                  ),
            ),
          ),
        );
    }

    // Return the measurement widget directly since indicators are now handled within each widget
    return measurementWidget;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final measurements = widget.question?.measurements ?? [];

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.question?.questionDescription ?? 'QPMD Page',
      ),
      body: measurements.isEmpty
          ? Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No measurements available for this question',
                  style: textTheme.bodyLarge?.copyWith(
                    color: AppColors.blackTint1,
                  ),
                ),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding:
                    const EdgeInsets.only(bottom: 90), // Leave space for FAB
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(12),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: measurements.length,
                      itemBuilder: (context, index) {
                        final measurement = measurements[index];
                        return _buildMeasurementWidget(measurement);
                      },
                      separatorBuilder: (_, __) => const Gap(8),
                    ),
                    const Gap(24),
                  ],
                ),
              ),
            ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: SizedBox(
          width: double.infinity,
          child: AppButton(
            text: 'Save',
            color: AppColors.primaryBlue,
            onPressed: _handleSave,
          ),
        ),
      ),
    );
  }
}
